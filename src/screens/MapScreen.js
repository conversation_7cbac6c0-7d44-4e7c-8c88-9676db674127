import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  TextInput,
  Alert,
  Linking,
} from 'react-native';
import MapView from '../components/MapView';
import LocationDetailsModal from '../components/LocationDetailsModal';
import OfflineMapManager from '../components/OfflineMapManager';
import treksData from '../data/treksData.json';
import { COLORS, SPACING, BORDER_RADIUS, SHADOWS } from '../utils/constants';
// Map configuration
const MAP_CONFIG = {
  defaultRegion: {
    latitude: 18.5204,
    longitude: 73.8567,
    latitudeDelta: 2.0,
    longitudeDelta: 2.0,
  },
  mapTypes: ['standard', 'satellite', 'hybrid', 'terrain'],
};

const MapScreen = ({ navigation }) => {
  const [locations] = useState(treksData);
  const [filteredLocations, setFilteredLocations] = useState(treksData);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [offlineModalVisible, setOfflineModalVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');
  const [mapType, setMapType] = useState('standard');

  // Filter locations based on search and category
  useEffect(() => {
    let filtered = locations;

    // Filter by category
    if (activeFilter !== 'all') {
      filtered = filtered.filter(location => location.category === activeFilter);
    }

    // Filter by search text
    if (searchText.trim()) {
      filtered = filtered.filter(location =>
        location.name.toLowerCase().includes(searchText.toLowerCase()) ||
        location.location.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    setFilteredLocations(filtered);
  }, [searchText, activeFilter, locations]);

  const handleLocationPress = (location) => {
    setSelectedLocation(location);
    setModalVisible(true);
  };

  const handleNavigate = (location) => {
    const { latitude, longitude } = location.coordinates;
    const url = `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`;
    
    Linking.canOpenURL(url)
      .then(supported => {
        if (supported) {
          Linking.openURL(url);
        } else {
          Alert.alert('Error', 'Unable to open navigation app');
        }
      })
      .catch(err => {
        console.error('Navigation error:', err);
        Alert.alert('Error', 'Unable to open navigation app');
      });
  };

  const handleViewDetails = (location) => {
    navigation.navigate('TrekDetails', { trek: location });
  };

  const toggleMapType = () => {
    const types = MAP_CONFIG.mapTypes;
    const currentIndex = types.indexOf(mapType);
    const nextIndex = (currentIndex + 1) % types.length;
    setMapType(types[nextIndex]);
  };

  const getTypeName = () => {
    const typeNames = {
      standard: 'Standard',
      satellite: 'Satellite',
      hybrid: 'Hybrid',
      terrain: 'Terrain',
    };
    return typeNames[mapType] || 'Standard';
  };

  const renderFilterButton = (filter, label, icon) => (
    <TouchableOpacity
      key={filter}
      style={[
        styles.filterButton,
        activeFilter === filter && styles.activeFilterButton,
      ]}
      onPress={() => setActiveFilter(filter)}
    >
      <Text style={[
        styles.filterIcon,
        activeFilter === filter && styles.activeFilterIcon,
      ]}>
        {icon}
      </Text>
      <Text style={[
        styles.filterText,
        activeFilter === filter && styles.activeFilterText,
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Explore Maharashtra</Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={styles.offlineButton}
            onPress={() => setOfflineModalVisible(true)}
          >
            <Text style={styles.offlineButtonText}>📱 Offline</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.styleButton} onPress={toggleMapType}>
            <Text style={styles.styleButtonText}>{getTypeName()}</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Text style={styles.searchIcon}>🔍</Text>
          <TextInput
            style={styles.searchInput}
            placeholder="Search locations..."
            placeholderTextColor={COLORS.textSecondary}
            value={searchText}
            onChangeText={setSearchText}
          />
          {searchText.length > 0 && (
            <TouchableOpacity onPress={() => setSearchText('')}>
              <Text style={styles.clearIcon}>✕</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Filter Buttons */}
      <View style={styles.filtersContainer}>
        {renderFilterButton('all', 'All', '🗺️')}
        {renderFilterButton('fort', 'Forts', '🏰')}
        {renderFilterButton('waterfall', 'Waterfalls', '💧')}
        {renderFilterButton('trek', 'Treks', '⛰️')}
      </View>

      {/* Map */}
      <View style={styles.mapContainer}>
        <MapView
          locations={filteredLocations}
          selectedLocation={selectedLocation}
          onLocationPress={handleLocationPress}
          mapType={mapType}
          showUserLocation={true}
          initialCenter={MAP_CONFIG.defaultRegion}
        />
      </View>

      {/* Results Count */}
      <View style={styles.resultsContainer}>
        <Text style={styles.resultsText}>
          {filteredLocations.length} location{filteredLocations.length !== 1 ? 's' : ''} found
        </Text>
      </View>

      {/* Location Details Modal */}
      <LocationDetailsModal
        visible={modalVisible}
        location={selectedLocation}
        onClose={() => setModalVisible(false)}
        onNavigate={handleNavigate}
        onViewDetails={handleViewDetails}
      />

      {/* Offline Map Manager */}
      <OfflineMapManager
        visible={offlineModalVisible}
        onClose={() => setOfflineModalVisible(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    backgroundColor: COLORS.background,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.backgroundSecondary,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '800',
    color: COLORS.text,
  },
  headerButtons: {
    flexDirection: 'row',
    gap: SPACING.sm,
  },
  offlineButton: {
    backgroundColor: COLORS.backgroundCard,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: COLORS.primary,
    ...SHADOWS.small,
  },
  offlineButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.primary,
  },
  styleButton: {
    backgroundColor: COLORS.backgroundCard,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    ...SHADOWS.small,
  },
  styleButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.primary,
  },
  searchContainer: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.backgroundCard,
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    ...SHADOWS.small,
  },
  searchIcon: {
    fontSize: 18,
    marginRight: SPACING.md,
    color: COLORS.textSecondary,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '500',
  },
  clearIcon: {
    fontSize: 16,
    color: COLORS.textSecondary,
    fontWeight: '600',
  },
  filtersContainer: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.md,
    gap: SPACING.sm,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.backgroundCard,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 1,
    borderColor: COLORS.backgroundSecondary,
  },
  activeFilterButton: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  filterIcon: {
    fontSize: 16,
    marginRight: SPACING.xs,
  },
  filterText: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.text,
  },
  activeFilterIcon: {
    color: COLORS.textInverse,
  },
  activeFilterText: {
    color: COLORS.textInverse,
  },
  mapContainer: {
    flex: 1,
    margin: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
    ...SHADOWS.medium,
  },
  resultsContainer: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    backgroundColor: COLORS.backgroundCard,
    borderTopWidth: 1,
    borderTopColor: COLORS.backgroundSecondary,
  },
  resultsText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default MapScreen;
